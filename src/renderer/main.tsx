import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles.css';

// Function to initialize the app
const initApp = () => {
  const rootElement = document.getElementById('root');
  if (!rootElement) {
    console.error('Root element not found');
    return;
  }

  const root = ReactDOM.createRoot(rootElement);

  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>
  );
};

// Initialize immediately if DOM is already loaded, otherwise wait for it
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}

// Handle hot module replacement in development
if (import.meta.hot) {
  import.meta.hot.accept();
}
