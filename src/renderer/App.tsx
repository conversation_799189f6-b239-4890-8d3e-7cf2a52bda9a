import React, { useState, useEffect, useRef } from 'react';
import { SearchBar } from './components/SearchBar';
import { Sidebar } from './components/Sidebar';
import { NotesView } from './components/NotesView';
import { Calculator } from './components/Calculator';
import { ClipboardView } from './components/ClipboardView';
import { useAppStore } from './store/appStore';

type ViewType = 'notes' | 'calculator' | 'clipboard';

function App() {
  // Temporary test to see if React is rendering
  console.log('App component is rendering');

  const [currentView, setCurrentView] = useState<ViewType>('notes');
  const [searchQuery, setSearchQuery] = useState('');
  const [sidebarWidth, setSidebarWidth] = useState(256); // 16rem in pixels
  const { selectedNote, setSelectedNote } = useAppStore();
  const resizeRef = useRef<HTMLDivElement>(null);
  const isResizing = useRef(false);

  // Listen for events from main process
  useEffect(() => {
    if (window.electronEvents) {
      // Handle create new note
      window.electronEvents.onCreateNewNote(() => {
        setCurrentView('notes');
        setSelectedNote(null);
        // Focus will be handled by the NotesView component
      });

      // Handle focus search
      window.electronEvents.onFocusSearch(() => {
        setCurrentView('notes');
        // Focus search bar - will be handled by SearchBar component
      });

      // Handle switch to calculator
      window.electronEvents.onSwitchToCalculator(() => {
        setCurrentView('calculator');
      });

      // Handle switch to clipboard
      window.electronEvents.onSwitchToClipboard(() => {
        setCurrentView('clipboard');
      });

      // Handle show preferences
      window.electronEvents.onShowPreferences(() => {
        // TODO: Implement preferences view
        console.log('Show preferences requested');
      });

      // Cleanup listeners on unmount
      return () => {
        window.electronEvents.removeAllListeners('create-new-note');
        window.electronEvents.removeAllListeners('focus-search');
        window.electronEvents.removeAllListeners('switch-to-calculator');
        window.electronEvents.removeAllListeners('switch-to-clipboard');
        window.electronEvents.removeAllListeners('show-preferences');
      };
    }
  }, [setSelectedNote]);

  // Handle sidebar resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    isResizing.current = true;
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing.current) return;
    
    const newWidth = Math.max(200, Math.min(400, e.clientX));
    setSidebarWidth(newWidth);
  };

  const handleMouseUp = () => {
    isResizing.current = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'notes':
        return (
          <NotesView 
            searchQuery={searchQuery}
            selectedNote={selectedNote}
            onSelectNote={setSelectedNote}
          />
        );
      case 'calculator':
        return <Calculator />;
      case 'clipboard':
        return <ClipboardView searchQuery={searchQuery} />;
      default:
        return <NotesView searchQuery={searchQuery} selectedNote={selectedNote} onSelectNote={setSelectedNote} />;
    }
  };

  const handleCloseWindow = () => {
    if (window.electronAPI) {
      window.electronAPI.hideWindow();
    }
  };

  // Temporary simple test
  return (
    <div style={{ padding: '20px', backgroundColor: 'red', color: 'white', fontSize: '24px' }}>
      <h1>Test - React is working!</h1>
      <p>Current view: {currentView}</p>
      <p>Search query: {searchQuery}</p>
      <button onClick={() => setCurrentView('calculator')}>Switch to Calculator</button>
    </div>
  );
}

export default App;
