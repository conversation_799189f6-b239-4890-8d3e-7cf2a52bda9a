import React, { useState, useEffect, useRef } from 'react';
import { SearchBar } from './components/SearchBar';
import { Sidebar } from './components/Sidebar';
import { NotesView } from './components/NotesView';
import { Calculator } from './components/Calculator';
import { ClipboardView } from './components/ClipboardView';
import { useAppStore } from './store/appStore';

type ViewType = 'notes' | 'calculator' | 'clipboard';

function App() {
  const [currentView, setCurrentView] = useState<ViewType>('notes');
  const [searchQuery, setSearchQuery] = useState('');
  const [sidebarWidth, setSidebarWidth] = useState(256); // 16rem in pixels
  const { selectedNote, setSelectedNote } = useAppStore();
  const resizeRef = useRef<HTMLDivElement>(null);
  const isResizing = useRef(false);

  // Listen for events from main process
  useEffect(() => {
    if (window.electronEvents) {
      // Handle create new note
      window.electronEvents.onCreateNewNote(() => {
        setCurrentView('notes');
        setSelectedNote(null);
        // Focus will be handled by the NotesView component
      });

      // Handle focus search
      window.electronEvents.onFocusSearch(() => {
        setCurrentView('notes');
        // Focus search bar - will be handled by SearchBar component
      });

      // Handle switch to calculator
      window.electronEvents.onSwitchToCalculator(() => {
        setCurrentView('calculator');
      });

      // Handle switch to clipboard
      window.electronEvents.onSwitchToClipboard(() => {
        setCurrentView('clipboard');
      });

      // Handle show preferences
      window.electronEvents.onShowPreferences(() => {
        // TODO: Implement preferences view
        console.log('Show preferences requested');
      });

      // Cleanup listeners on unmount
      return () => {
        window.electronEvents.removeAllListeners('create-new-note');
        window.electronEvents.removeAllListeners('focus-search');
        window.electronEvents.removeAllListeners('switch-to-calculator');
        window.electronEvents.removeAllListeners('switch-to-clipboard');
        window.electronEvents.removeAllListeners('show-preferences');
      };
    }
  }, [setSelectedNote]);

  // Handle sidebar resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    isResizing.current = true;
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing.current) return;
    
    const newWidth = Math.max(200, Math.min(400, e.clientX));
    setSidebarWidth(newWidth);
  };

  const handleMouseUp = () => {
    isResizing.current = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'notes':
        return (
          <NotesView 
            searchQuery={searchQuery}
            selectedNote={selectedNote}
            onSelectNote={setSelectedNote}
          />
        );
      case 'calculator':
        return <Calculator />;
      case 'clipboard':
        return <ClipboardView searchQuery={searchQuery} />;
      default:
        return <NotesView searchQuery={searchQuery} selectedNote={selectedNote} onSelectNote={setSelectedNote} />;
    }
  };

  const handleCloseWindow = () => {
    if (window.electronAPI) {
      window.electronAPI.hideWindow();
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Custom Title Bar with Drag Area */}
      <div 
        className="flex-shrink-0 bg-white border-b border-gray-200"
        style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}
      >
        <div className="flex items-center justify-between px-4 py-3">
          {/* Search Bar */}
          <div className="flex-1 max-w-md" style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}>
            <SearchBar
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder={
                currentView === 'notes' ? 'Search notes...' :
                currentView === 'clipboard' ? 'Search clipboard...' :
                'Search...'
              }
            />
          </div>

          {/* Close Button */}
          <button
            onClick={handleCloseWindow}
            className="ml-4 p-1.5 rounded-full hover:bg-gray-100 transition-colors"
            style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
          >
            <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Resizable Sidebar */}
        <div 
          className="bg-white border-r border-gray-200 flex flex-col relative"
          style={{ width: sidebarWidth }}
        >
          <Sidebar 
            currentView={currentView}
            onViewChange={setCurrentView}
          />
          
          {/* Resize Handle */}
          <div
            ref={resizeRef}
            onMouseDown={handleMouseDown}
            className="absolute top-0 right-0 w-1 h-full cursor-col-resize hover:bg-blue-500 transition-colors opacity-0 hover:opacity-100"
            style={{ right: '-2px' }}
          />
        </div>

        {/* Main View */}
        <div className="flex-1 overflow-hidden">
          {renderCurrentView()}
        </div>
      </div>
    </div>
  );
}

export default App;
