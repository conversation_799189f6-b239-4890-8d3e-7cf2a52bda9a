import { app, BrowserWindow } from 'electron';
import { join } from 'path';
import { SimpleTray } from './tray';
import { registerShortcuts } from './shortcuts';
import { setupIpcHandlers } from './ipc';
import { NotesDB } from '../database/notes';
import { ClipboardManager } from './clipboard';

class NexusApp {
  private mainWindow: BrowserWindow | null = null;
  private tray: SimpleTray | null = null;
  private notesDB: NotesDB | null = null;
  private clipboardManager: ClipboardManager | null = null;

  constructor() {
    this.setupApp();
  }

  private setupApp() {
    // Ensure single instance
    const gotTheLock = app.requestSingleInstanceLock();

    if (!gotTheLock) {
      app.quit();
      return;
    }

    // Handle second instance attempt
    app.on('second-instance', () => {
      // Someone tried to run a second instance, focus our window instead
      if (this.mainWindow) {
        if (this.mainWindow.isMinimized()) {
          this.mainWindow.restore();
        }
        this.mainWindow.show();
        this.mainWindow.focus();
      }
    });

    // Handle app ready
    app.whenReady().then(() => {
      this.createWindow();
      this.setupTray();
      this.setupShortcuts();
      this.setupDatabase();
      this.setupClipboard();
      this.setupIPC();
    });

    // Handle window closed
    app.on('window-all-closed', () => {
      // On macOS, keep app running even when all windows are closed
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Handle app activation (macOS)
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    // Handle before quit
    app.on('before-quit', () => {
      console.log('App is quitting...');
      if (this.clipboardManager) {
        this.clipboardManager.stop();
      }
      if (this.tray) {
        this.tray.destroy();
      }
    });

  }

  private createWindow() {
    // Prevent creating multiple windows
    if (this.mainWindow) {
      this.mainWindow.show();
      this.mainWindow.focus();
      return;
    }

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 600,
      minHeight: 400,
      titleBarStyle: 'hiddenInset',
      trafficLightPosition: { x: -100, y: -100 }, // Hide traffic lights
      backgroundColor: '#ffffff', // White background
      show: false, // Don't show until ready
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, '../preload/preload.js'),
        webSecurity: false, // Disable for localhost in development
      },
    });

    // Load the React app
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:5173');
      // Auto-open DevTools for debugging
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(join(__dirname, '../../renderer/index.html'));
    }

    // Handle window events
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Hide window instead of closing on macOS
    this.mainWindow.on('close', (event) => {
      if (process.platform === 'darwin') {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });

    // Handle content loading
    this.mainWindow.webContents.on('did-finish-load', () => {
      console.log('Renderer content loaded');
      // Show and focus the window after content loads
      this.mainWindow?.show();
      this.mainWindow?.focus();
      this.mainWindow?.moveTop();
    });

    this.mainWindow.webContents.on('did-fail-load', (_event, errorCode, errorDescription, validatedURL) => {
      console.error('Failed to load renderer:', errorCode, errorDescription, validatedURL);
    });

    // Window is already shown, just log when it's ready
    this.mainWindow.once('ready-to-show', () => {
      console.log('Window ready to show');
    });
  }

  private setupTray() {
    if (this.mainWindow) {
      this.tray = new SimpleTray(this.mainWindow);
    }
  }

  private setupShortcuts() {
    if (this.mainWindow) {
      registerShortcuts(this.mainWindow);
    }
  }

  private setupDatabase() {
    const dbPath = join(app.getPath('userData'), 'nexus.db');
    this.notesDB = new NotesDB(dbPath);
  }

  private setupClipboard() {
    this.clipboardManager = new ClipboardManager();
    this.clipboardManager.start();
  }

  private setupIPC() {
    if (this.notesDB && this.clipboardManager && this.mainWindow) {
      setupIpcHandlers(this.notesDB, this.clipboardManager, this.mainWindow);
    }
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  public getNotesDB(): NotesDB | null {
    return this.notesDB;
  }

  public getClipboardManager(): ClipboardManager | null {
    return this.clipboardManager;
  }
}

// Create the app instance only if we're running in Electron
let nexusApp: NexusApp | null = null;

// Check if we're running in Electron context
if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
  nexusApp = new NexusApp();
}

export default nexusApp;
