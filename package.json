{"name": "nexus", "version": "1.0.0", "description": "AI-friendly note-taking and productivity app for macOS", "main": "dist/main/main/main.js", "scripts": {"predev": "rm -rf dist", "dev": "concurrently \"npm run dev:vite\" \"npm run dev:main\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:main": "tsc -p tsconfig.main.json --watch", "dev:electron": "wait-on http://localhost:5173 && wait-on dist/main/main/main.js && mkdir -p dist/main/database && cp src/database/schema.sql dist/main/database/ && NODE_ENV=development electron .", "build": "npm run build:main && npm run build:renderer && npm run copy:assets", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "vite build", "copy:assets": "cp src/database/schema.sql dist/main/database/", "build:electron": "electron-builder", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "keywords": ["electron", "react", "typescript", "notes", "productivity"], "author": "Nexus Team", "license": "MIT", "dependencies": {"better-sqlite3": "^9.0.0", "electron": "^27.0.0", "mathjs": "^12.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "concurrently": "^8.0.0", "electron-builder": "^24.0.0", "electron-rebuild": "^3.2.9", "eslint": "^8.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-electron": "^0.15.0", "wait-on": "^7.0.0"}, "build": {"appId": "com.nexus.app", "productName": "Nexus", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "target": "dmg"}}}